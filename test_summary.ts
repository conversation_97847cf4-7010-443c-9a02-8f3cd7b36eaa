import fetch from 'node-fetch';

// Summarization API v2 URL
const SUMMARIZE_URL =
  'https://clovastudio.apigw.ntruss.com/v1/api-tools/summarization/v2';
// Chat Completions v3 URL
const CHAT_URL =
  'https://clovastudio.apigw.ntruss.com/chat/v3/completions';

// 1) Summarization tool 정의
const tools = [
  {
    name: 'summarize',
    description: '긴 텍스트를 문단 단위로 요약합니다.',
    parameters: {
      type: 'object',
      properties: {
        texts: {
          type: 'array',
          items: { type: 'string' },
          description: '요약할 텍스트 배열'
        },
        autoSentenceSplitter: { type: 'boolean' },
        segMaxSize: { type: 'integer' },
        segMinSize: { type: 'integer' },
        segCount:     { type: 'integer' }
      },
      required: ['texts']
    }
  }
];

async function callSummarizeApi(args: {
  texts: string[];
  autoSentenceSplitter?: boolean;
  segMaxSize?: number;
  segMinSize?: number;
  segCount?: number;
}) {
  const res = await fetch(SUMMARIZE_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-NCP-APIGW-API-KEY-ID': process.env.NCP_API_KEY_ID!,
      'X-NCP-APIGW-API-KEY':    process.env.NCP_API_KEY!
    },
    body: JSON.stringify({
      texts: args.texts,
      autoSentenceSplitter: args.autoSentenceSplitter ?? true,
      segCount: args.segCount ?? -1,
      segMaxSize: args.segMaxSize ?? 200,
      segMinSize: args.segMinSize ?? 50,
      includeAiFilters: false
    })
  });
  if (!res.ok) throw new Error(`요약 API 오류 ${res.status}`);
  const { summaries } = (await res.json()) as {
    summaries: Array<{ summary: string }>;
  };
  return summaries[0].summary;
}

async function chatWithAutoSummary(userText: string) {
  // 2) Chat Completions v3 호출 (Function Calling auto)
  const chatResp = await fetch(CHAT_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-NCP-APIGW-API-KEY-ID': process.env.NCP_API_KEY_ID!,
      'X-NCP-APIGW-API-KEY':    process.env.NCP_API_KEY!
    },
    body: JSON.stringify({
      model: 'hcx-005',
      messages: [{ role: 'user', content: userText }],
      tools,
      toolChoice: 'auto',
      maxTokens: 2048
    })
  }).then(r => r.json());

  // 3) 모델이 summarize 툴을 호출했으면, 실제 Summarization API 실행
  const callInfo = chatResp.choices[0].message?.toolCalls?.[0];
  if (callInfo?.name === 'summarize') {
    const args = JSON.parse(callInfo.arguments!);
    return await callSummarizeApi(args);
  }

  // 4) 함수 호출 없이 직접 요약 응답 받은 경우
  return chatResp.choices[0].message?.content as string;
}

// 사용 예시
(async () => {
  const longText = '여기에 토큰이 많은 긴 본문…';
  const summary = await chatWithAutoSummary(longText);
  console.log('[요약 결과]', summary);
})();
